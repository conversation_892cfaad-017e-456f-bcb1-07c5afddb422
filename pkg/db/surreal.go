package db

import (
	"log"

	"github.com/google/uuid"
	"github.com/surrealdb/surrealdb.go"
)

// TODO: Make these connection details configurable.
const (
	dbEndpoint = "ws://172.31.61.195:8000"
	dbUser     = "root"
	dbPass     = "root"
	dbNS       = "test"
	dbName     = "test"
)

// InitDB initializes and returns a connection to SurrealDB.
func InitDB() (*surrealdb.DB, error) {
	db, err := surrealdb.New(dbEndpoint)
	if err != nil {
		return nil, err
	}

	// Authenticate using the low-level Send method
	authParams := []interface{}{map[string]interface{}{
		"user": dbUser,
		"pass": dbPass,
	}}
	err = db.Send(uuid.New().String(), "signin", authParams...)
	if err != nil {
		return nil, err
	}

	// Select namespace and database using the low-level Send method
	useParams := []interface{}{dbNS, dbName}
	err = db.Send(uuid.New().String(), "use", useParams...)
	if err != nil {
		return nil, err
	}

	log.Println("Successfully connected to SurrealDB.")
	return db, nil
}

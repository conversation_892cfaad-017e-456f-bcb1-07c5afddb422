package handler

import (
	"alarm_distribution/pkg/model"
	"alarm_distribution/pkg/notifiers"
	"encoding/json"
	"log"

	"github.com/google/uuid"
	"github.com/surrealdb/surrealdb.go"
	"github.com/valyala/fasthttp"
)

const (
	alertManagerTable = "alertmanager_alerts"
)

// AlertHandler handles incoming alert requests.
type AlertHandler struct {
	DB *surrealdb.DB
}

// HandleAlertManager handles alerts from AlertManager.
func (h *AlertHandler) HandleAlertManager(ctx *fasthttp.RequestCtx) {
	contentType := string(ctx.Request.Header.ContentType())
	if contentType != "application/json" {
		ctx.SetStatusCode(fasthttp.StatusUnsupportedMediaType)
		ctx.SetBodyString("Unsupported Media Type: must be application/json")
		return
	}

	var data model.AlertManagerData
	if err := json.Unmarshal(ctx.PostBody(), &data); err != nil {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		ctx.SetBodyString("Bad Request: could not parse JSON body")
		log.Printf("Error unmarshalling AlertManager data: %v", err)
		return
	}

	// Save the raw data to SurrealDB using the low-level Send method
	createParams := []interface{}{alertManagerTable, data}
	err := h.DB.Send(uuid.New().String(), "create", createParams...)
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		ctx.SetBodyString("Internal Server Error: could not save data to database")
		log.Printf("Error saving data to SurrealDB: %v", err)
		return
	}

	log.Printf("Successfully stored alert from receiver %s", data.Receiver)

	// Trigger notifications
	for _, alert := range data.Alerts {
		severity := alert.Labels["severity"]
		notifier := notifiers.GetNotifier(severity)
		if err := notifier.Send(alert); err != nil {
			log.Printf("Failed to send notification for alert %s: %v", alert.Labels["alertname"], err)
			// Depending on requirements, you might want to continue or handle the error differently.
		}
	}

	ctx.SetStatusCode(fasthttp.StatusOK)
	ctx.SetBodyString("Alert received and processed.")
}

package model

import "time"

// AlertManagerData is the structure of the data sent by AlertManager.
type AlertManagerData struct {
	Receiver          string    `json:"receiver"`
	Status            string    `json:"status"`
	Alerts            []Alert   `json:"alerts"`
	GroupLabels       Labels    `json:"groupLabels"`
	CommonLabels      Labels    `json:"commonLabels"`
	CommonAnnotations Annotations `json:"commonAnnotations"`
	ExternalURL       string    `json:"externalURL"`
	Version           string    `json:"version"`
	GroupKey          string    `json:"groupKey"`
	TruncatedAlerts   int       `json:"truncatedAlerts"`
}

// Alert is a single alert.
type Alert struct {
	Status       string      `json:"status"`
	Labels       Labels      `json:"labels"`
	Annotations  Annotations `json:"annotations"`
	StartsAt     time.Time   `json:"startsAt"`
	EndsAt       time.Time   `json:"endsAt"`
	GeneratorURL string      `json:"generatorURL"`
	Fingerprint  string      `json:"fingerprint"`
}

// Labels is a set of key/value label pairs.
type Labels map[string]string

// Annotations is a set of key/value annotation pairs.
type Annotations map[string]string

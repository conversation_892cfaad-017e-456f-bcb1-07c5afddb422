package notifiers

import (
	"alarm_distribution/pkg/model"
	"log"
	"strings"
)

// NotificationChannel represents a generic notification channel.
type NotificationChannel interface {
	Send(alert model.Alert) error
}

// LogNotifier is a simple notifier that just logs the alert.
type LogNotifier struct{}

// Send logs the alert information.
func (n *LogNotifier) Send(alert model.Alert) error {
	log.Printf("NOTIFY: Alert '%s' with severity '%s'. Summary: %s",
		alert.Labels["alertname"],
		alert.Labels["severity"],
		alert.Annotations["summary"],
	)
	return nil
}

// SlackNotifier is a placeholder for a real Slack notifier.
type SlackNotifier struct{}

// Send would send a notification to Slack.
func (n *SlackNotifier) Send(alert model.Alert) error {
	log.Printf("SLACK NOTIFY: Alert '%s' with severity '%s'. Summary: %s",
		alert.Labels["alertname"],
		alert.Labels["severity"],
		alert.Annotations["summary"],
	)
	// Here you would implement the actual Slack API call.
	return nil
}

// GetNotifier returns a notifier based on the alert's severity.
func GetNotifier(severity string) NotificationChannel {
	switch strings.ToLower(severity) {
	case "critical", "high":
		// For critical alerts, maybe notify Slack.
		return &SlackNotifier{}
	case "warning", "info", "": // Default to log notifier
		return &LogNotifier{}
	default:
		return &LogNotifier{}
	}
}

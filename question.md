# Web Docs

[SurrealDB Go SDK](https://pkg.go.dev/github.com/surrealdb/surrealdb.go)
[SurrealDB Architecture](https://surrealdb.com/docs/surrealdb/introduction/architecture)

# Introduction

这是一个用于解析通过 POST 请求获取投递的监控告警数据的处理项目，将接收到的监控告警数据先写入 SurrealDB 中，然后根据其告警等级，选择不同的通知渠道并发送告警。

1、使用 fasthttp 来创建对应的 HTTP Server，并且处理 HTTP 请求
2、首先 POST 请求传入的 Body 可能会有很多种类，比如开源 AlertManager、阿里云 SLS 等等，所以你需要通过项目的维度，来实现针对 AlertManager 格式的解析，阿里云 SLS 格式的解析，或者自定义格式的解析等等。
3、你可以通过提供的 Docs 来判断，使用哪种方式来存储数据最好
4、
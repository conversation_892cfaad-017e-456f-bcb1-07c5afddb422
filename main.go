package main

import (
	"alarm_distribution/pkg/db"
	"alarm_distribution/pkg/handler"
	"log"

	"github.com/valyala/fasthttp"
)

func main() {
	// Initialize SurrealDB connection
	sdb, err := db.InitDB()
	if err != nil {
		log.Fatalf("Failed to connect to SurrealDB: %v", err)
	}
	defer sdb.Close()

	// Create handlers
	alertHandler := &handler.AlertHandler{DB: sdb}

	// Define the request handler routing logic
	requestHandler := func(ctx *fasthttp.RequestCtx) {
		switch string(ctx.Path()) {
		case "/alerts/alertmanager":
			alertHandler.HandleAlertManager(ctx)
		default:
			ctx.Error("Not Found", fasthttp.StatusNotFound)
		}
	}

	// Start the server
	addr := ":8080"
	log.Printf("Starting server on %s\n", addr)

	if err := fasthttp.ListenAndServe(addr, requestHandler); err != nil {
		log.Fatalf("Error in ListenAndServe: %s", err)
	}
}
